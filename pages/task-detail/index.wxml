<!--pages/task-detail/index.wxml-->
<view class="task-detail-page">
  <!-- 调试信息 -->
  <view class="debug-info" style="padding: 20rpx; background: #f0f0f0; margin: 20rpx;">
    <text>模式: {{mode}}</text>
    <text>加载状态: {{isLoading}}</text>
    <text>任务ID: {{taskId}}</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-animation">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 任务表单 -->
  <view class="task-form" wx:if="{{!isLoading}}">
    <text style="display: block; padding: 20rpx;">表单区域 - 模式: {{mode}}</text>

    <!-- 任务标题 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">任务标题</text>
        <text class="required">*</text>
      </view>
      <input
        class="form-input"
        placeholder="请输入任务标题"
        value="{{formData.title}}"
        bindinput="onTitleInput"
        disabled="{{mode === 'view'}}"
        maxlength="50"
      />
    </view>

    <!-- 任务描述 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">任务描述</text>
      </view>
      <textarea
        class="form-textarea"
        placeholder="请输入任务描述（可选）"
        value="{{formData.description}}"
        bindinput="onDescriptionInput"
        disabled="{{mode === 'view'}}"
        maxlength="200"
        auto-height
      />
    </view>

    <!-- 分类和优先级 -->
    <view class="form-row">
      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">分类</text>
        </view>
        <view class="form-picker">
          <text class="picker-text">{{formData.category}}</text>
        </view>
      </view>

      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">优先级</text>
        </view>
        <view class="form-picker">
          <text class="picker-text priority-{{formData.priority}}">
            {{formData.priority}}
          </text>
        </view>
      </view>
    </view>

    <!-- 截止时间 -->
    <view class="form-row">
      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">截止日期</text>
        </view>
        <view class="form-picker">
          <text class="picker-text">{{formData.dueDate || '选择日期'}}</text>
        </view>
      </view>

      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">截止时间</text>
        </view>
        <view class="form-picker">
          <text class="picker-text">{{formData.dueTime || '选择时间'}}</text>
        </view>
      </view>
    </view>

    <!-- 标签 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">标签</text>
        <text class="label-tip">用逗号分隔多个标签</text>
      </view>
      <input
        class="form-input"
        placeholder="如：重要,紧急,工作"
        value="{{formData.tags}}"
        bindinput="onTagsInput"
        disabled="{{mode === 'view'}}"
        maxlength="100"
      />
    </view>

    <!-- 提醒设置 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">提醒设置</text>
      </view>
      <view class="reminder-setting">
        <text class="reminder-text">提醒: {{formData.reminderEnabled ? '开启' : '关闭'}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{!isLoading}}">
    <view class="button-group">
      <button class="btn btn-secondary" bindtap="navigateBack">返回</button>
      <button class="btn btn-primary" bindtap="saveTask" wx:if="{{mode === 'create'}}">
        创建任务
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
// pages/task-detail/index.js
const app = getApp();

Page({
  data: {
    // 页面模式：create, edit, view
    mode: 'view',
    taskId: '',

    // 任务数据
    task: {
      title: '',
      description: '',
      category: '焦虑管理',
      priority: 'medium',
      dueDate: '',
      dueTime: '',
      tags: [],
      reminder: {
        enabled: false,
        time: ''
      }
    },

    // 表单数据
    formData: {
      title: '',
      description: '',
      category: '焦虑管理',
      priority: 'medium',
      dueDate: '',
      dueTime: '',
      tags: '',
      reminderEnabled: false,
      reminderTime: ''
    },

    // 选项数据
    categoryOptions: [
      '焦虑管理', '学习', '工作', '生活', '健康', '其他'
    ],

    priorityOptions: [
      { value: 'low', label: '普通', color: '#95A5A6' },
      { value: 'medium', label: '重要', color: '#F39C12' },
      { value: 'high', label: '紧急', color: '#E74C3C' }
    ],

    // 状态
    isLoading: false,
    isSaving: false,

    // 计算属性
    priorityIndex: 1,
    priorityLabel: '重要'
  },

  onLoad(options) {
    const { mode = 'view', id = '' } = options;

    this.setData({
      mode: mode,
      taskId: id
    });

    // 设置导航栏标题
    const titleMap = {
      create: '创建任务',
      edit: '编辑任务',
      view: '任务详情'
    };

    wx.setNavigationBarTitle({
      title: titleMap[mode] || '任务详情'
    });

    if (mode === 'create') {
      this.initCreateMode();
    } else if (id) {
      this.loadTaskDetail(id);
    }
  },

  // 初始化创建模式
  initCreateMode() {
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

    this.setData({
      isLoading: false,
      'formData.dueDate': app.formatDate(tomorrow, 'YYYY-MM-DD'),
      'formData.dueTime': '18:00'
    });

    this.updatePriorityDisplay();
  },

  // 更新优先级显示
  updatePriorityDisplay() {
    const { formData, priorityOptions } = this.data;
    const priorityIndex = priorityOptions.findIndex(item => item.value === formData.priority);
    const priorityLabel = priorityOptions.find(item => item.value === formData.priority)?.label || '重要';

    this.setData({
      priorityIndex: priorityIndex >= 0 ? priorityIndex : 1,
      priorityLabel: priorityLabel
    });
  },

  // 加载任务详情
  async loadTaskDetail(taskId) {
    try {
      this.setData({ isLoading: true });

      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'getDetail',
          taskId: taskId
        }
      });

      if (res.result.code === 0) {
        const task = res.result.data;
        this.setData({
          task: task,
          formData: {
            title: task.title || '',
            description: task.description || '',
            category: task.category || '焦虑管理',
            priority: task.priority || 'medium',
            dueDate: task.dueDate ? app.formatDate(task.dueDate, 'YYYY-MM-DD') : '',
            dueTime: task.dueDate ? app.formatDate(task.dueDate, 'HH:mm') : '',
            tags: task.tags ? task.tags.join(', ') : '',
            reminderEnabled: task.reminder?.enabled || false,
            reminderTime: task.reminder?.time || ''
          }
        });

        this.updatePriorityDisplay();
      } else {
        app.showError(res.result.message);
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载任务详情失败:', error);
      app.showError('加载失败，请重试');
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 表单输入处理
  onTitleInput(e) {
    this.setData({
      'formData.title': e.detail.value
    });
  },

  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  onTagsInput(e) {
    this.setData({
      'formData.tags': e.detail.value
    });
  },

  // 选择分类
  onCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.category': this.data.categoryOptions[index]
    });
  },

  // 选择优先级
  onPriorityChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.priority': this.data.priorityOptions[index].value
    });
    this.updatePriorityDisplay();
  },

  // 选择日期
  onDateChange(e) {
    this.setData({
      'formData.dueDate': e.detail.value
    });
  },

  // 选择时间
  onTimeChange(e) {
    this.setData({
      'formData.dueTime': e.detail.value
    });
  },

  // 切换提醒
  onReminderToggle(e) {
    this.setData({
      'formData.reminderEnabled': e.detail.value
    });
  },

  // 保存任务
  async saveTask() {
    const { mode, taskId, formData } = this.data;

    // 验证表单
    if (!formData.title.trim()) {
      app.showError('请输入任务标题');
      return;
    }

    try {
      this.setData({ isSaving: true });

      // 构建任务数据
      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category,
        priority: formData.priority,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
        reminder: {
          enabled: formData.reminderEnabled,
          time: formData.reminderTime
        }
      };

      // 处理截止时间
      if (formData.dueDate && formData.dueTime) {
        taskData.dueDate = new Date(`${formData.dueDate} ${formData.dueTime}`);
      } else if (formData.dueDate) {
        taskData.dueDate = new Date(`${formData.dueDate} 23:59`);
      }

      let res;
      if (mode === 'create') {
        res = await wx.cloud.callFunction({
          name: 'tasks',
          data: {
            action: 'create',
            ...taskData
          }
        });
      } else {
        res = await wx.cloud.callFunction({
          name: 'tasks',
          data: {
            action: 'update',
            taskId: taskId,
            ...taskData
          }
        });
      }

      if (res.result.code === 0) {
        app.showSuccess(mode === 'create' ? '任务创建成功' : '任务更新成功');
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('保存任务失败:', error);
      app.showError('保存失败，请重试');
    } finally {
      this.setData({ isSaving: false });
    }
  },

  // 删除任务
  async deleteTask() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个任务吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await wx.cloud.callFunction({
              name: 'tasks',
              data: {
                action: 'delete',
                taskId: this.data.taskId
              }
            });

            if (result.result.code === 0) {
              app.showSuccess('任务已删除');
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            } else {
              app.showError(result.result.message);
            }
          } catch (error) {
            console.error('删除任务失败:', error);
            app.showError('删除失败，请重试');
          }
        }
      }
    });
  },

  // 切换编辑模式
  toggleEditMode() {
    const newMode = this.data.mode === 'view' ? 'edit' : 'view';
    this.setData({ mode: newMode });

    wx.setNavigationBarTitle({
      title: newMode === 'edit' ? '编辑任务' : '任务详情'
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
});
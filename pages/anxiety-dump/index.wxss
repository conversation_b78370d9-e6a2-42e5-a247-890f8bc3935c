/* pages/anxiety-dump/index.wxss */

.anxiety-dump-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为tabbar留出空间 */
}

/* 页面头部 */
.anxiety-dump-header {
  padding: 60rpx 32rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 简化背景效果，提高兼容性 */
.anxiety-dump-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.05);
  border-radius: 50%;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 2;
}

.header-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.care-icon {
  width: 48rpx;
  height: 48rpx;
}

.header-text {
  flex: 1;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: rgba(255,255,255,0.8);
  line-height: 1.4;
}

/* 温馨提示卡片 */
.tip-card {
  background: rgba(255,255,255,0.2);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4rpx;
}

.tip-text {
  font-size: 22rpx;
  color: rgba(255,255,255,0.7);
  line-height: 1.4;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 32rpx;
}

/* 输入区域 */
.input-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
  margin-bottom: 24rpx;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a202c;
}

.word-count-badge {
  background: #f7fafc;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
}

.word-count {
  font-size: 24rpx;
  font-weight: 600;
  color: #4a5568;
  margin-right: 4rpx;
}

.word-unit {
  font-size: 20rpx;
  color: #718096;
}

.input-container {
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 320rpx;
  padding: 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: #f8fafc;
  color: #2d3748;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.text-input:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.text-input::placeholder {
  color: #a0aec0;
  font-style: normal;
}

.input-indicator {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: #f7fafc;
  border-radius: 12rpx;
}

.indicator-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #cbd5e0;
  margin-right: 12rpx;
  transition: background-color 0.3s ease;
}

.indicator-dot.active {
  background: #48bb78;
}

.indicator-text {
  font-size: 24rpx;
  color: #4a5568;
}

/* 智能提示 */
.smart-hints {
  margin-top: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
}

.hints-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 20rpx;
  text-align: center;
}

.hints-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.hint-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.hint-card:active {
  background: #667eea;
  border-color: #667eea;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.hint-text {
  font-size: 24rpx;
  color: #2d3748;
  line-height: 1.4;
  text-align: center;
}

.hint-card:active .hint-text {
  color: #ffffff;
}

/* 工具栏 */
.toolbar {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
  margin-bottom: 24rpx;
}

.voice-section {
  display: flex;
  align-items: center;
}

.voice-container {
  display: flex;
  align-items: center;
}

.voice-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
  margin-right: 16rpx;
}

.voice-button::after {
  border: none;
}

.voice-button:active {
  transform: scale(0.95);
}

.voice-button.recording {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  animation: pulse 1s infinite;
}

.voice-button:disabled {
  background: #e2e8f0;
  box-shadow: none;
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.voice-info {
  display: flex;
  flex-direction: column;
}

.voice-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 4rpx;
}

.voice-tip {
  font-size: 22rpx;
  color: #718096;
}

/* 操作区域 */
.action-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
}

/* 语音识别结果 */
.voice-result {
  margin: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border-left: 4rpx solid var(--primary-color);
  animation: slideUp 0.3s ease-out;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.result-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.result-confidence {
  font-size: var(--font-size-caption);
  color: var(--success-color);
}

.result-content {
  margin-bottom: var(--spacing-lg);
}

.result-text {
  font-size: var(--font-size-body);
  color: var(--text-primary);
  line-height: 1.6;
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.btn-clear {
  background: #f7fafc;
  color: #4a5568;
  border: 2rpx solid #e2e8f0;
}

.btn-clear:active {
  background: #edf2f7;
  transform: translateY(2rpx);
}

.btn-clear:disabled {
  background: #f7fafc;
  color: #cbd5e0;
}

.btn-submit {
  background: #e2e8f0;
  color: #a0aec0;
  border: 2rpx solid #e2e8f0;
}

.btn-submit.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-color: #667eea;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.btn-submit.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.btn-submit:disabled {
  background: #e2e8f0;
  color: #a0aec0;
  border-color: #e2e8f0;
  box-shadow: none;
}

.analysis-info {
  text-align: center;
  padding: 16rpx;
  background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
  border-radius: 12rpx;
  border: 1rpx solid #9ae6b4;
}

.info-text {
  font-size: 24rpx;
  color: #2f855a;
  line-height: 1.4;
}

/* 分析进度 */
.analysis-progress {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #bae6fd;
  animation: pulse-bg 2s infinite;
}

@keyframes pulse-bg {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #0369a1;
}

.progress-percent {
  font-size: 24rpx;
  font-weight: 600;
  color: #0284c7;
}

.progress-bar {
  height: 8rpx;
  background: #e0f2fe;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-20rpx); }
  100% { transform: translateX(20rpx); }
}

.progress-step {
  font-size: 22rpx;
  color: #0369a1;
  text-align: center;
  display: block;
  animation: fade-in-out 1s infinite alternate;
}

@keyframes fade-in-out {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* 历史记录 */
.history-section {
  margin: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.history-header:active {
  background: var(--bg-secondary);
}

.history-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}

.history-list {
  animation: slideDown 0.3s ease-out;
}

.history-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: var(--bg-secondary);
}

.history-content {
  flex: 1;
}

.history-text {
  display: block;
  font-size: var(--font-size-body);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.history-status {
  margin-left: var(--spacing-md);
}

.status-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
}

.status-tag.completed {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.status-tag.processing {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.status-tag.pending {
  background: rgba(52, 152, 219, 0.1);
  color: var(--info-color);
}

/* 录音状态弹窗 */
.recording-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xxl);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.recording-animation {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.wave-circle {
  position: absolute;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  animation: wave 2s infinite;
}

.wave-1 {
  width: 120rpx;
  height: 120rpx;
  animation-delay: 0s;
}

.wave-2 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 200rpx;
  height: 200rpx;
  animation-delay: 1s;
}

.mic-icon {
  width: 64rpx;
  height: 64rpx;
  z-index: 10;
}

.recording-text {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.recording-time {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.recording-tip {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 语音处理弹窗 */
.processing-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-animation {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

.processing-text {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
}

/* 动画定义 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes wave {
  0% { transform: scale(0.8); opacity: 1; }
  100% { transform: scale(1.2); opacity: 0; }
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes slideDown {
  from { max-height: 0; opacity: 0; }
  to { max-height: 1000rpx; opacity: 1; }
}

<!--pages/anxiety-dump/index.wxml-->
<view class="anxiety-dump-page page-container">
  <!-- 页面头部 -->
  <view class="anxiety-dump-header">
    <view class="header-content">
      <view class="header-icon">
        <image src="/images/icons/heart-care.png" class="care-icon" />
      </view>
      <view class="header-text">
        <text class="page-title">倾诉你的焦虑</text>
        <text class="page-subtitle">AI会倾听并帮你找到解决方案</text>
      </view>
    </view>

    <!-- 温馨提示卡片 -->
    <view class="tip-card">
      <view class="tip-icon">💡</view>
      <view class="tip-content">
        <text class="tip-title">温馨提示</text>
        <text class="tip-text">你的倾诉内容完全私密，只有你能看到</text>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-header">
        <text class="input-label">说出你的困扰</text>
        <view class="word-count-badge">
          <text class="word-count">{{wordCount}}</text>
          <text class="word-unit">字</text>
        </view>
      </view>

      <view class="input-container">
        <textarea
          class="text-input"
          placeholder="{{placeholder}}"
          value="{{inputText}}"
          bindinput="onTextInput"
          bindblur="onInputBlur"
          bindfocus="onInputFocus"
          auto-height
          maxlength="-1"
          show-confirm-bar="{{false}}"
          adjust-position="{{true}}"
        />

        <!-- 输入状态指示器 -->
        <view class="input-indicator" wx:if="{{inputText.length > 0}}">
          <view class="indicator-dot {{canSubmit ? 'active' : ''}}"></view>
          <text class="indicator-text">{{canSubmit ? '可以开始分析了' : '再多写一些吧'}}</text>
        </view>
      </view>

      <!-- 智能提示 -->
      <view class="smart-hints" wx:if="{{showHints && inputText.length === 0}}">
        <text class="hints-title">💭 不知道从何说起？</text>
        <view class="hints-grid">
          <view
            class="hint-card"
            wx:for="{{hintTexts}}"
            wx:key="*this"
            bindtap="selectHint"
            data-hint="{{item}}"
          >
            <text class="hint-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 工具栏 -->
    <view class="toolbar">
      <!-- 语音输入 -->
      <view class="voice-section">
        <view class="voice-container">
          <button
            class="voice-button {{isRecording ? 'recording' : ''}}"
            bindtouchstart="startRecording"
            bindtouchend="stopRecording"
            bindtouchcancel="cancelRecording"
            disabled="{{isProcessingVoice}}"
          >
            <image
              src="/images/icons/{{isRecording ? 'recording' : 'microphone'}}.png"
              class="voice-icon"
            />
          </button>
          <view class="voice-info">
            <text class="voice-label">语音输入</text>
            <text class="voice-tip">{{voiceTip}}</text>
          </view>
        </view>
      </view>
    </view>

  <!-- 语音识别结果 -->
  <view class="voice-result" wx:if="{{voiceResult}}">
    <view class="result-header">
      <text class="result-title">语音识别结果</text>
      <text class="result-confidence">准确度: {{voiceConfidence}}%</text>
    </view>
    <view class="result-content">
      <text class="result-text">{{voiceResult}}</text>
    </view>
    <view class="result-actions">
      <button class="btn btn-secondary btn-small" bindtap="reRecord">重新录音</button>
      <button class="btn btn-primary btn-small" bindtap="confirmVoiceResult">确认添加</button>
    </view>
  </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view class="action-buttons">
        <button
          class="btn btn-clear"
          bindtap="clearInput"
          disabled="{{inputText.length === 0}}"
        >
          <image src="/images/icons/clear.png" class="btn-icon" />
          清空
        </button>
        <button
          class="btn btn-submit {{canSubmit ? 'active' : ''}}"
          bindtap="submitAnxiety"
          disabled="{{!canSubmit}}"
          loading="{{isSubmitting}}"
        >
          <image src="/images/icons/{{isSubmitting ? 'loading' : 'analysis'}}.png" class="btn-icon" />
          {{isSubmitting ? 'AI分析中...' : '开始AI分析'}}
        </button>
      </view>

      <!-- 分析说明 -->
      <view class="analysis-info" wx:if="{{canSubmit && !isSubmitting}}">
        <text class="info-text">🤖 AI将为你分析情绪并生成专属解决方案</text>
      </view>

      <!-- 分析进度 -->
      <view class="analysis-progress" wx:if="{{isSubmitting}}">
        <view class="progress-header">
          <text class="progress-title">AI正在分析中...</text>
          <text class="progress-percent">{{analysisProgress}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{analysisProgress}}%"></view>
        </view>
        <text class="progress-step">{{currentStep}}</text>
      </view>
    </view>
  </view>

  <!-- 历史记录入口 -->
  <view class="history-section" wx:if="{{hasHistory}}">
    <view class="history-header" bindtap="toggleHistory">
      <text class="history-title">历史倾诉记录</text>
      <image 
        src="/images/icons/arrow-{{showHistory ? 'up' : 'down'}}.png" 
        class="arrow-icon"
      />
    </view>
    
    <view class="history-list" wx:if="{{showHistory}}">
      <view 
        class="history-item"
        wx:for="{{historyList}}" 
        wx:key="_id"
        bindtap="loadHistoryItem"
        data-id="{{item._id}}"
      >
        <view class="history-content">
          <text class="history-text">{{item.preview}}</text>
          <text class="history-time">{{item.timeText}}</text>
        </view>
        <view class="history-status">
          <text class="status-tag {{item.status}}">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 录音状态弹窗 -->
<view class="recording-modal" wx:if="{{isRecording}}">
  <view class="modal-overlay"></view>
  <view class="modal-content">
    <view class="recording-animation">
      <view class="wave-circle wave-1"></view>
      <view class="wave-circle wave-2"></view>
      <view class="wave-circle wave-3"></view>
      <image src="/images/icons/microphone-white.png" class="mic-icon" />
    </view>
    <text class="recording-text">正在录音...</text>
    <text class="recording-time">{{recordingTime}}s</text>
    <text class="recording-tip">松开结束录音，上滑取消</text>
  </view>
</view>

<!-- 语音处理弹窗 -->
<view class="processing-modal" wx:if="{{isProcessingVoice}}">
  <view class="modal-overlay"></view>
  <view class="modal-content">
    <view class="loading-animation">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="processing-text">正在识别语音...</text>
  </view>
</view>

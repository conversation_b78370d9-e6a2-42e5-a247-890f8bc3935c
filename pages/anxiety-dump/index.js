// pages/anxiety-dump/index.js
const app = getApp();

Page({
  data: {
    inputText: '',
    wordCount: 0,
    canSubmit: false,
    isSubmitting: false,
    showHints: false,
    
    // 语音相关
    isRecording: false,
    isProcessingVoice: false,
    recordingTime: 0,
    voiceResult: '',
    voiceConfidence: 0,
    voiceTip: '长按录音',
    
    // 历史记录
    hasHistory: false,
    showHistory: false,
    historyList: [],
    
    // 引导文案和提示
    guideText: '无论是学习压力、工作困扰还是生活烦恼，都可以在这里自由表达。我会帮你分析并提供具体的解决方案。',
    placeholder: '在这里，你可以安全地表达内心的焦虑和困扰...',
    hintTexts: [
      '我最近感到很焦虑，因为...',
      '我担心自己无法完成...',
      '面对即将到来的...，我感到压力很大',
      '我不知道该如何处理...',
      '每当想到...，我就感到不安'
    ],
    
    // 定时器
    recordingTimer: null,
    draftTimer: null,

    // 分析状态
    analysisProgress: 0,
    analysisSteps: [
      '正在理解你的困扰...',
      '分析情绪状态...',
      '识别关键问题...',
      '生成解决方案...',
      '完成分析报告...'
    ],
    currentStep: ''
  },

  onLoad() {
    console.log('焦虑倾诉页面加载');

    // 真机调试信息
    const systemInfo = wx.getSystemInfoSync();
    console.log('设备信息:', {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight
    });

    this.initPage();
  },

  onShow() {
    console.log('焦虑倾诉页面显示');
  },

  onReady() {
    console.log('焦虑倾诉页面渲染完成');
  },

  onShow() {
    console.log('焦虑倾诉页面显示');
    this.loadDraft();
    this.loadHistory();
  },

  onHide() {
    console.log('焦虑倾诉页面隐藏');
    this.clearTimers();
  },

  onUnload() {
    console.log('焦虑倾诉页面卸载');
    this.clearTimers();
  },

  // 初始化页面
  initPage() {
    // 设置随机占位符
    this.setRandomPlaceholder();
    
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再使用倾诉功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/index/index' });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }
  },

  // 文字输入处理
  onTextInput(e) {
    const text = e.detail.value;
    const wordCount = text.length;
    
    this.setData({
      inputText: text,
      wordCount: wordCount,
      canSubmit: text.trim().length >= 10,
      showHints: false
    });
    
    // 防抖保存草稿
    this.debounceSaveDraft(text);
  },

  onInputFocus() {
    this.setData({ showHints: true });
  },

  onInputBlur() {
    // 延迟隐藏提示，给用户点击时间
    setTimeout(() => {
      this.setData({ showHints: false });
    }, 200);
  },

  // 选择提示文本
  selectHint(e) {
    const hint = e.currentTarget.dataset.hint;
    const currentText = this.data.inputText;
    const newText = currentText ? `${currentText}\n${hint}` : hint;
    
    this.setData({
      inputText: newText,
      wordCount: newText.length,
      canSubmit: newText.trim().length >= 10,
      showHints: false
    });
    
    this.saveDraft(newText);
  },

  // 语音录制相关
  startRecording() {
    console.log('开始录音');
    
    // 检查录音权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.record']) {
          wx.authorize({
            scope: 'scope.record',
            success: () => this.doStartRecording(),
            fail: () => this.showRecordPermissionTip()
          });
        } else {
          this.doStartRecording();
        }
      }
    });
  },

  doStartRecording() {
    this.setData({
      isRecording: true,
      recordingTime: 0,
      voiceTip: '正在录音...'
    });

    // 开始录音计时
    this.recordingTimer = setInterval(() => {
      const time = this.data.recordingTime + 1;
      this.setData({ recordingTime: time });
      
      // 最长录音60秒
      if (time >= 60) {
        this.stopRecording();
      }
    }, 1000);

    // 开始录音
    wx.startRecord({
      success: (res) => {
        console.log('录音成功:', res);
        this.processVoiceFile(res.tempFilePath);
      },
      fail: (err) => {
        console.error('录音失败:', err);
        this.setData({
          isRecording: false,
          voiceTip: '录音失败，请重试'
        });
        this.clearRecordingTimer();
      }
    });
  },

  stopRecording() {
    console.log('停止录音');
    
    if (this.data.isRecording) {
      wx.stopRecord();
      this.setData({
        isRecording: false,
        voiceTip: '长按录音'
      });
      this.clearRecordingTimer();
    }
  },

  cancelRecording() {
    console.log('取消录音');
    this.stopRecording();
  },

  // 处理语音文件
  async processVoiceFile(filePath) {
    this.setData({
      isProcessingVoice: true,
      voiceTip: '正在识别...'
    });

    try {
      // 调用语音识别云函数
      const res = await wx.cloud.callFunction({
        name: 'speechToText',
        data: { filePath }
      });

      if (res.result.code === 0) {
        const { text, confidence } = res.result.data;
        this.setData({
          voiceResult: text,
          voiceConfidence: Math.round(confidence * 100),
          voiceTip: '识别完成'
        });
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('语音识别失败:', error);
      app.showError('语音识别失败，请重试');
      this.setData({ voiceTip: '识别失败' });
    } finally {
      this.setData({ isProcessingVoice: false });
    }
  },

  // 重新录音
  reRecord() {
    this.setData({
      voiceResult: '',
      voiceConfidence: 0
    });
  },

  // 确认语音结果
  confirmVoiceResult() {
    const voiceText = this.data.voiceResult;
    const currentText = this.data.inputText;
    const newText = currentText ? `${currentText}\n${voiceText}` : voiceText;
    
    this.setData({
      inputText: newText,
      wordCount: newText.length,
      canSubmit: newText.trim().length >= 10,
      voiceResult: '',
      voiceConfidence: 0
    });
    
    this.saveDraft(newText);
    app.showSuccess('语音内容已添加');
  },

  // 提交焦虑内容
  async submitAnxiety() {
    if (!this.data.canSubmit) return;

    const content = this.data.inputText.trim();

    this.setData({
      isSubmitting: true,
      analysisProgress: 0,
      currentStep: this.data.analysisSteps[0]
    });

    // 模拟分析进度
    this.startAnalysisProgress();

    try {
      const res = await wx.cloud.callFunction({
        name: 'anxiety',
        data: {
          action: 'submit',
          content: content,
          type: 'text'
        }
      });

      if (res.result.code === 0) {
        // 分析完成
        this.setData({
          analysisProgress: 100,
          currentStep: '分析完成！'
        });

        // 清除草稿
        this.clearDraft();

        // 延迟跳转，让用户看到完成状态
        setTimeout(() => {
          const recordId = res.result.data.recordId;
          wx.navigateTo({
            url: `/pages/analysis-result/index?recordId=${recordId}`
          });
        }, 500);

        app.showSuccess('分析完成！');
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('提交失败:', error);
      app.showError('分析失败，请重试');
    } finally {
      this.setData({
        isSubmitting: false,
        analysisProgress: 0,
        currentStep: ''
      });
    }
  },

  // 开始分析进度动画
  startAnalysisProgress() {
    let progress = 0;
    let stepIndex = 0;

    const progressTimer = setInterval(() => {
      progress += Math.random() * 15 + 5; // 随机增加5-20的进度

      if (progress >= 90) {
        progress = 90; // 最多到90%，等待真实结果
        clearInterval(progressTimer);
      }

      // 更新步骤
      const newStepIndex = Math.floor((progress / 100) * this.data.analysisSteps.length);
      if (newStepIndex !== stepIndex && newStepIndex < this.data.analysisSteps.length) {
        stepIndex = newStepIndex;
        this.setData({
          currentStep: this.data.analysisSteps[stepIndex]
        });
      }

      this.setData({ analysisProgress: progress });
    }, 800);

    // 保存定时器引用，以便清理
    this.progressTimer = progressTimer;
  },

  // 清空输入
  clearInput() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前输入的内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            inputText: '',
            wordCount: 0,
            canSubmit: false
          });
          this.clearDraft();
        }
      }
    });
  },

  // 历史记录相关
  toggleHistory() {
    this.setData({
      showHistory: !this.data.showHistory
    });
  },

  async loadHistory() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'anxiety',
        data: {
          action: 'getHistory',
          limit: 5
        }
      });

      if (res.result.code === 0) {
        const history = res.result.data.map(item => ({
          ...item,
          preview: item.content.substring(0, 30) + (item.content.length > 30 ? '...' : ''),
          timeText: this.formatTime(item.createdAt),
          statusText: this.getStatusText(item.status)
        }));

        this.setData({
          historyList: history,
          hasHistory: history.length > 0
        });
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  },

  loadHistoryItem(e) {
    const id = e.currentTarget.dataset.id;
    const item = this.data.historyList.find(h => h._id === id);
    
    if (item) {
      wx.showModal({
        title: '加载历史内容',
        content: '是否要加载这条历史记录的内容？当前输入的内容将被替换。',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              inputText: item.content,
              wordCount: item.content.length,
              canSubmit: item.content.trim().length >= 10
            });
            this.saveDraft(item.content);
          }
        }
      });
    }
  },

  // 草稿管理
  debounceSaveDraft: app.debounce(function(text) {
    this.saveDraft(text);
  }, 1000),

  saveDraft(text) {
    try {
      wx.setStorageSync('anxiety_draft', {
        text,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('保存草稿失败:', error);
    }
  },

  loadDraft() {
    try {
      const draft = wx.getStorageSync('anxiety_draft');
      if (draft && draft.text) {
        // 如果草稿不超过24小时，则恢复
        if (Date.now() - draft.timestamp < 24 * 60 * 60 * 1000) {
          this.setData({
            inputText: draft.text,
            wordCount: draft.text.length,
            canSubmit: draft.text.trim().length >= 10
          });
        }
      }
    } catch (error) {
      console.error('加载草稿失败:', error);
    }
  },

  clearDraft() {
    try {
      wx.removeStorageSync('anxiety_draft');
    } catch (error) {
      console.error('清除草稿失败:', error);
    }
  },

  // 工具方法
  setRandomPlaceholder() {
    const placeholders = [
      '在这里，你可以安全地表达内心的焦虑和困扰...',
      '告诉我什么让你感到焦虑，我会帮你分析和解决',
      '无论是学习、工作还是生活上的压力，都可以在这里倾诉',
      '描述一下你现在的感受，让我们一起面对这些挑战'
    ];
    
    const randomIndex = Math.floor(Math.random() * placeholders.length);
    this.setData({
      placeholder: placeholders[randomIndex]
    });
  },

  showRecordPermissionTip() {
    wx.showModal({
      title: '需要录音权限',
      content: '语音输入功能需要录音权限，请在设置中开启',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting();
        }
      }
    });
  },

  formatTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return app.formatDate(time, 'HH:mm');
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return app.formatDate(time, 'MM-DD');
    }
  },

  getStatusText(status) {
    const statusMap = {
      pending: '待处理',
      processing: '分析中',
      completed: '已完成',
      failed: '失败'
    };
    return statusMap[status] || '未知';
  },

  clearTimers() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    if (this.draftTimer) {
      clearTimeout(this.draftTimer);
      this.draftTimer = null;
    }
  },

  clearRecordingTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }
});

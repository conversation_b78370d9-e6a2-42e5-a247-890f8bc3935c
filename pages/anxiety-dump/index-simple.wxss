/* 应急简化版样式 - 确保真机兼容性 */

.anxiety-dump-page {
  background: #f8f9fa;
  min-height: calc(100vh - 120rpx);
  padding-bottom: 120rpx;
}

/* 页面头部 - 简化版 */
.anxiety-dump-header {
  padding: 30px 16px 20px;
  background: #667eea;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  margin-right: 12px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.page-subtitle {
  font-size: 13px;
  color: rgba(255,255,255,0.8);
}

.tip-card {
  background: rgba(255,255,255,0.2);
  border-radius: 10px;
  padding: 12px;
  display: flex;
  align-items: center;
}

.tip-icon {
  font-size: 16px;
  margin-right: 8px;
}

.tip-title {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 2px;
}

.tip-text {
  font-size: 11px;
  color: rgba(255,255,255,0.7);
}

/* 主要内容区域 - 简化版 */
.main-content {
  padding: 16px;
}

.input-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.word-count-badge {
  background: #f7fafc;
  border-radius: 10px;
  padding: 4px 8px;
}

.word-count {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-right: 2px;
}

.word-unit {
  font-size: 10px;
  color: #718096;
}

.text-input {
  width: 100%;
  min-height: 160px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: #f8fafc;
  color: #2d3748;
}

.text-input:focus {
  border-color: #667eea;
  background: #ffffff;
}

/* 工具栏 - 简化版 */
.toolbar {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.voice-container {
  display: flex;
  align-items: center;
}

.voice-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin-right: 12px;
}

.voice-icon {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 2px;
}

.voice-label {
  font-size: 13px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 2px;
}

.voice-tip {
  font-size: 11px;
  color: #718096;
}

/* 操作区域 - 简化版 */
.action-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-clear {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-submit {
  background: #e2e8f0;
  color: #a0aec0;
}

.btn-submit.active {
  background: #667eea;
  color: #ffffff;
}

.analysis-info {
  text-align: center;
  padding: 8px;
  background: #f0fff4;
  border-radius: 6px;
  border: 1px solid #9ae6b4;
}

.info-text {
  font-size: 12px;
  color: #2f855a;
}

/* 分析进度 - 简化版 */
.analysis-progress {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #bae6fd;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-title {
  font-size: 13px;
  font-weight: 500;
  color: #0369a1;
}

.progress-percent {
  font-size: 12px;
  font-weight: 600;
  color: #0284c7;
}

.progress-bar {
  height: 4px;
  background: #e0f2fe;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: #0ea5e9;
  border-radius: 2px;
}

.progress-step {
  font-size: 11px;
  color: #0369a1;
  text-align: center;
}

/* 智能提示 - 简化版 */
.smart-hints {
  margin-top: 16px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.hints-title {
  font-size: 13px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 10px;
  text-align: center;
}

.hints-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hint-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  flex: 1;
  min-width: calc(50% - 4px);
}

.hint-text {
  font-size: 12px;
  color: #2d3748;
  text-align: center;
}

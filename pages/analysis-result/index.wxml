<!--pages/analysis-result/index.wxml-->
<view class="analysis-result-page">
  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{isLoading}}">
    <view class="loading-animation">
      <view class="loading-circle"></view>
      <view class="loading-circle"></view>
      <view class="loading-circle"></view>
    </view>
    <text class="loading-text">AI正在分析你的焦虑...</text>
    <text class="loading-tip">这可能需要几秒钟时间</text>
  </view>

  <!-- 分析完成 -->
  <view class="result-content" wx:if="{{!isLoading && analysisResult}}">
    <!-- 情绪分析结果 -->
    <view class="emotion-analysis card">
      <view class="card-header">
        <text class="card-title">情绪分析</text>
        <text class="confidence-score">准确度 {{analysisResult.confidencePercent}}%</text>
      </view>
      
      <view class="emotion-tags">
        <text 
          class="emotion-tag {{item}}"
          wx:for="{{analysisResult.emotions}}" 
          wx:key="*this"
        >
          {{getEmotionText(item)}}
        </text>
      </view>
      
      <view class="analysis-summary">
        <view class="summary-item">
          <text class="summary-label">焦虑类别</text>
          <text class="summary-value">{{analysisResult.category}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">紧急程度</text>
          <text class="summary-value urgency-{{analysisResult.urgency}}">
            {{analysisResult.urgencyText}}
          </text>
        </view>
        <view class="summary-item">
          <text class="summary-label">复杂程度</text>
          <text class="summary-value complexity-{{analysisResult.complexity}}">
            {{analysisResult.complexityText}}
          </text>
        </view>
      </view>
      
      <view class="keywords-section" wx:if="{{analysisResult.keywords.length > 0}}">
        <text class="keywords-title">关键词</text>
        <view class="keywords-list">
          <text 
            class="keyword-tag"
            wx:for="{{analysisResult.keywords}}" 
            wx:key="*this"
          >
            {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 任务建议 -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">AI为你生成的行动计划</text>
        <text class="section-desc">将焦虑转化为具体的行动步骤</text>
      </view>
      
      <view class="tasks-list">
        <view 
          class="task-card {{item.priority}}"
          wx:for="{{generatedTasks}}" 
          wx:key="index"
        >
          <view class="task-header">
            <view class="task-priority">
              <text class="priority-dot"></text>
              <text class="priority-text">{{getPriorityText(item.priority)}}</text>
            </view>
            <text class="task-time">预计 {{item.estimatedTime}} 分钟</text>
          </view>
          
          <view class="task-content">
            <text class="task-title">{{item.title}}</text>
            <text class="task-description">{{item.description}}</text>
          </view>
          
          <view class="task-suggestion" wx:if="{{item.suggestedTime}}">
            <text class="suggestion-label">建议时间</text>
            <text class="suggestion-time">{{formatSuggestedTime(item.suggestedTime)}}</text>
          </view>
          
          <view class="task-actions">
            <button 
              class="btn btn-secondary btn-small" 
              bindtap="editTask"
              data-index="{{index}}"
            >
              编辑
            </button>
            <button 
              class="btn btn-primary btn-small" 
              bindtap="acceptTask"
              data-index="{{index}}"
            >
              接受
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="btn btn-secondary" 
        bindtap="regenerateTasks"
        disabled="{{isRegenerating}}"
      >
        {{isRegenerating ? '重新生成中...' : '重新生成'}}
      </button>
      <button 
        class="btn btn-primary" 
        bindtap="acceptAllTasks"
        disabled="{{acceptedTasks.length === generatedTasks.length}}"
      >
        全部接受 ({{acceptedTasks.length}}/{{generatedTasks.length}})
      </button>
    </view>

    <!-- 反馈区域 -->
    <view class="feedback-section card">
      <view class="card-header">
        <text class="card-title">这次分析对你有帮助吗？</text>
      </view>
      
      <view class="rating-section">
        <view class="rating-stars">
          <text 
            class="star {{index < rating ? 'active' : ''}}"
            wx:for="{{5}}" 
            wx:key="*this"
            bindtap="setRating"
            data-rating="{{index + 1}}"
          >
            ★
          </text>
        </view>
        <text class="rating-text">{{getRatingText(rating)}}</text>
      </view>
      
      <textarea 
        class="feedback-input"
        placeholder="可以告诉我们你的想法和建议..."
        value="{{feedbackText}}"
        bindinput="onFeedbackInput"
        maxlength="200"
      />
      
      <button 
        class="btn btn-primary btn-small" 
        bindtap="submitFeedback"
        disabled="{{rating === 0}}"
      >
        提交反馈
      </button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-section" wx:if="{{!isLoading && !analysisResult}}">
    <image src="/images/icons/error.png" class="error-icon" />
    <text class="error-title">分析失败</text>
    <text class="error-message">{{errorMessage}}</text>
    <button class="btn btn-primary" bindtap="retryAnalysis">重试</button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 任务编辑弹窗 -->
<view class="edit-modal" wx:if="{{showEditModal}}">
  <view class="modal-overlay" bindtap="hideEditModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">编辑任务</text>
    </view>
    
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">任务标题</text>
        <input 
          class="form-input"
          value="{{editingTask.title}}"
          bindinput="onEditTitle"
          placeholder="请输入任务标题"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">任务描述</text>
        <textarea 
          class="form-textarea"
          value="{{editingTask.description}}"
          bindinput="onEditDescription"
          placeholder="请输入任务描述"
          maxlength="200"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">优先级</text>
        <view class="priority-options">
          <text 
            class="priority-option {{editingTask.priority === item.value ? 'active' : ''}}"
            wx:for="{{priorityOptions}}" 
            wx:key="value"
            bindtap="selectPriority"
            data-priority="{{item.value}}"
          >
            {{item.text}}
          </text>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">预计时间（分钟）</text>
        <input 
          class="form-input"
          type="number"
          value="{{editingTask.estimatedTime}}"
          bindinput="onEditTime"
          placeholder="预计完成时间"
        />
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="hideEditModal">取消</button>
      <button class="btn btn-primary" bindtap="saveEditedTask">保存</button>
    </view>
  </view>
</view>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>焦虑分析显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2C3E50;
        }
        .confidence-score {
            font-size: 14px;
            color: #7F8C8D;
        }
        .emotion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }
        .emotion-tag {
            background: #E3F2FD;
            color: #1976D2;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
        }
        .analysis-summary {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .summary-label {
            font-size: 14px;
            color: #7F8C8D;
        }
        .summary-value {
            font-size: 14px;
            font-weight: 500;
            color: #2C3E50;
        }
        .urgency-high, .complexity-high {
            color: #E74C3C;
        }
        .urgency-medium, .complexity-medium {
            color: #F39C12;
        }
        .urgency-low, .complexity-low {
            color: #27AE60;
        }
        .keywords-section {
            border-top: 1px solid #E9ECEF;
            padding-top: 16px;
            margin-top: 16px;
        }
        .keywords-title {
            font-size: 14px;
            color: #7F8C8D;
            margin-bottom: 8px;
            display: block;
        }
        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }
        .keyword-tag {
            background: #F8F9FA;
            color: #495057;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>焦虑分析显示测试</h1>
    
    <div class="card">
        <div class="card-header">
            <span class="card-title">情绪分析</span>
            <span class="confidence-score">准确度 85%</span>
        </div>
        
        <div class="emotion-tags">
            <span class="emotion-tag">😰 焦虑</span>
            <span class="emotion-tag">😟 担心</span>
        </div>
        
        <div class="analysis-summary">
            <div class="summary-item">
                <span class="summary-label">焦虑类别</span>
                <span class="summary-value">工作</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">紧急程度</span>
                <span class="summary-value urgency-medium">中</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">复杂程度</span>
                <span class="summary-value complexity-medium">中等</span>
            </div>
        </div>
        
        <div class="keywords-section">
            <span class="keywords-title">关键词</span>
            <div class="keywords-list">
                <span class="keyword-tag">试用期</span>
                <span class="keyword-tag">考核</span>
                <span class="keyword-tag">完成</span>
            </div>
        </div>
    </div>

    <div class="card">
        <h3>不同紧急程度和复杂程度的显示效果：</h3>
        
        <div class="analysis-summary">
            <div class="summary-item">
                <span class="summary-label">紧急程度 - 高</span>
                <span class="summary-value urgency-high">高</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">紧急程度 - 中</span>
                <span class="summary-value urgency-medium">中</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">紧急程度 - 低</span>
                <span class="summary-value urgency-low">低</span>
            </div>
            
            <div class="summary-item">
                <span class="summary-label">复杂程度 - 复杂</span>
                <span class="summary-value complexity-high">复杂</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">复杂程度 - 中等</span>
                <span class="summary-value complexity-medium">中等</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">复杂程度 - 简单</span>
                <span class="summary-value complexity-low">简单</span>
            </div>
        </div>
    </div>

    <script>
        console.log('测试数据结构:');
        console.log({
            "emotions": ["焦虑", "担心"],
            "keywords": ["试用期", "考核", "完成"],
            "urgency": "medium",
            "complexity": "medium", 
            "category": "工作",
            "confidence": 0.85,
            "urgencyText": "中",
            "complexityText": "中等",
            "confidencePercent": 85
        });
    </script>
</body>
</html>

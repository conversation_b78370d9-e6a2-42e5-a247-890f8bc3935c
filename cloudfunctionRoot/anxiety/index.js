// 焦虑倾诉云函数
const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 硅基智能API配置
const SILICONFLOW_CONFIG = {
  baseURL: 'https://api.siliconflow.cn/v1',
  model: 'deepseek-ai/DeepSeek-V2.5', // 使用DeepSeek模型
  apiKey: process.env.AI_API_KEY || '', // 从环境变量获取API密钥
  timeout: 30000, // 30秒超时
  maxRetries: 3 // 最大重试次数
};

// AI分析提示词模板
const AI_PROMPTS = {
  // 焦虑分析提示词
  anxietyAnalysis: `你是一位专业的心理健康分析师。请分析用户的焦虑内容，并返回结构化的分析结果。

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "emotions": ["情绪1", "情绑2"],
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "urgency": "low|medium|high",
  "complexity": "low|medium|high",
  "category": "学习|工作|家庭|健康|人际关系|其他",
  "confidence": 0.85
}

分析要求：
1. emotions: 识别用户的主要情绪状态（如焦虑、压力、恐惧、担心等）
2. keywords: 提取3-5个关键词，反映焦虑的核心内容
3. urgency: 评估紧急程度（low=不紧急，medium=中等紧急，high=非常紧急）
4. complexity: 评估问题复杂程度（low=简单，medium=中等，high=复杂）
5. category: 将焦虑内容分类到最相关的类别
6. confidence: 分析结果的置信度（0-1之间的小数）

用户焦虑内容：`,

  // 任务生成提示词
  taskGeneration: `你是一位专业的心理健康顾问和任务管理专家。基于用户的焦虑分析结果，请生成2-4个具体可执行的任务建议。

请严格按照以下JSON格式返回任务列表，不要包含任何其他文字：

{
  "tasks": [
    {
      "title": "任务标题",
      "description": "详细的任务描述，说明具体要做什么",
      "priority": "low|medium|high",
      "estimatedTime": 30,
      "category": "immediate|short_term|long_term"
    }
  ]
}

任务生成要求：
1. 任务要具体、可执行、有针对性
2. 根据焦虑的紧急程度和复杂度调整任务数量和类型
3. 包含立即可做的缓解措施和长期解决方案
4. estimatedTime为预估完成时间（分钟）
5. category分为immediate（立即执行）、short_term（短期内）、long_term（长期规划）

焦虑分析结果：
`
};

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'submit':
        return await submitAnxiety(data, wxContext);
      case 'getAnalysis':
        return await getAnalysis(data, wxContext);
      case 'getHistory':
        return await getHistory(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 提交焦虑内容
async function submitAnxiety(data, wxContext) {
  const { OPENID } = wxContext;
  const { content, type = 'text', voiceUrl = '' } = data;
  
  // 验证输入
  if (!content || content.trim().length < 10) {
    return { code: 1001, message: '内容太短，请至少输入10个字符' };
  }
  
  try {
    // 保存焦虑记录
    const anxietyCollection = db.collection('anxietyRecords');
    const record = {
      userId: OPENID,
      input: {
        type: type,
        content: content.trim(),
        voiceUrl: voiceUrl,
        duration: type === 'voice' ? data.duration || 0 : 0
      },
      status: 'processing',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const createResult = await anxietyCollection.add({
      data: record
    });
    
    const recordId = createResult._id;
    
    // 更新用户统计
    await updateUserStats(OPENID, 'anxietyRecord');

    // 同步处理AI分析
    const analysisResult = await processAIAnalysis(recordId, content, OPENID);

    return {
      code: 0,
      message: '分析完成',
      data: {
        recordId: recordId,
        status: analysisResult.status,
        aiAnalysis: analysisResult.aiAnalysis,
        generatedTasks: analysisResult.generatedTasks,
        processingTime: analysisResult.processingTime
      }
    };
    
  } catch (error) {
    console.error('提交焦虑内容失败:', error);
    return { code: 3001, message: '提交失败' };
  }
}

// 获取分析结果
async function getAnalysis(data, wxContext) {
  const { OPENID } = wxContext;
  const { recordId } = data;
  
  try {
    const anxietyCollection = db.collection('anxietyRecords');
    const recordQuery = await anxietyCollection.doc(recordId).get();
    
    if (!recordQuery.data || recordQuery.data.userId !== OPENID) {
      return { code: 1003, message: '记录不存在或无权限访问' };
    }
    
    const record = recordQuery.data;
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        recordId: recordId,
        status: record.status,
        aiAnalysis: record.aiAnalysis || null,
        generatedTasks: record.generatedTasks || [],
        processingTime: record.processingTime || 0,
        createdAt: record.createdAt
      }
    };
    
  } catch (error) {
    console.error('获取分析结果失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取历史记录
async function getHistory(data, wxContext) {
  const { OPENID } = wxContext;
  const { limit = 10, skip = 0 } = data;
  
  try {
    const anxietyCollection = db.collection('anxietyRecords');
    const historyQuery = await anxietyCollection
      .where({
        userId: OPENID
      })
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(limit)
      .field({
        _id: true,
        'input.content': true,
        status: true,
        createdAt: true
      })
      .get();
    
    const history = historyQuery.data.map(item => ({
      _id: item._id,
      content: item.input.content,
      status: item.status,
      createdAt: item.createdAt
    }));
    
    return {
      code: 0,
      message: '获取成功',
      data: history
    };
    
  } catch (error) {
    console.error('获取历史记录失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 调用硅基智能API
async function callSiliconFlowAPI(messages, retryCount = 0) {
  try {
    if (!SILICONFLOW_CONFIG.apiKey) {
      throw new Error('AI_API_KEY环境变量未配置');
    }

    console.log('发送API请求，模型:', 'deepseek-ai/DeepSeek-V3');
    console.log('API Key前缀:', SILICONFLOW_CONFIG.apiKey.substring(0, 10) + '...');

    const response = await axios.post(
      `https://api.siliconflow.cn/v1/chat/completions`,
      {
        model: "deepseek-ai/DeepSeek-V3",
        messages: messages,
        temperature: 0.1,
        max_tokens: 4000,
        stream: false
      },
      {
        headers: {
          'Authorization': `Bearer ${SILICONFLOW_CONFIG.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: SILICONFLOW_CONFIG.timeout
      }
    );

    if (response.data && response.data.choices && response.data.choices[0]) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('调用硅基智能API失败:', error.message);

    // 详细错误信息
    if (error.response) {
      console.error('API响应状态:', error.response.status);
      console.error('API响应数据:', JSON.stringify(error.response.data, null, 2));
      console.error('API响应头:', JSON.stringify(error.response.headers, null, 2));
    } else if (error.request) {
      console.error('请求配置:', JSON.stringify(error.config, null, 2));
      console.error('没有收到响应:', error.request);
    }

    // 重试机制
    if (retryCount < SILICONFLOW_CONFIG.maxRetries) {
      console.log(`第${retryCount + 1}次重试...`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
      return callSiliconFlowAPI(messages, retryCount + 1);
    }

    throw error;
  }
}

// 使用AI进行焦虑分析
async function aiAnxietyAnalysis(content) {
  try {
    const messages = [
      {
        role: 'user',
        content: AI_PROMPTS.anxietyAnalysis + content
      }
    ];

    const response = await callSiliconFlowAPI(messages);

    // 尝试解析JSON响应
    try {
      // 清理响应内容，移除可能的markdown格式
      let cleanResponse = response.trim();

      // 移除markdown代码块标记
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const analysisResult = JSON.parse(cleanResponse);

      // 验证返回结果的格式
      if (!analysisResult.emotions || !analysisResult.keywords || !analysisResult.urgency) {
        throw new Error('AI返回结果格式不完整');
      }

      return analysisResult;
    } catch (parseError) {
      console.error('解析AI分析结果失败:', parseError.message);
      console.error('原始响应:', response);
      throw new Error('AI返回结果格式错误');
    }
  } catch (error) {
    console.error('AI焦虑分析失败:', error.message);
    throw error;
  }
}

// 使用AI生成任务建议
async function aiTaskGeneration(content, analysis) {
  try {
    const analysisText = JSON.stringify(analysis, null, 2);
    const messages = [
      {
        role: 'user',
        content: AI_PROMPTS.taskGeneration + analysisText + '\n\n用户原始内容：' + content
      }
    ];

    const response = await callSiliconFlowAPI(messages);

    // 尝试解析JSON响应
    try {
      // 清理响应内容，移除可能的markdown格式
      let cleanResponse = response.trim();

      // 移除markdown代码块标记
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const taskResult = JSON.parse(cleanResponse);

      if (!taskResult.tasks || !Array.isArray(taskResult.tasks)) {
        throw new Error('AI返回的任务格式不正确');
      }

      // 为每个任务添加建议时间
      const tasksWithTime = taskResult.tasks.map((task, index) => ({
        ...task,
        suggestedTime: getSuggestedTime(analysis.urgency, index)
      }));

      return tasksWithTime;
    } catch (parseError) {
      console.error('解析AI任务生成结果失败:', parseError.message);
      console.error('原始响应:', response);
      throw new Error('AI返回任务格式错误');
    }
  } catch (error) {
    console.error('AI任务生成失败:', error.message);
    throw error;
  }
}

// AI分析处理（同步）
async function processAIAnalysis(recordId, content, userId) {
  try {
    console.log('开始AI分析:', recordId);

    const startTime = Date.now();
    let aiAnalysis;
    let generatedTasks;
    let status = 'completed';

    try {
      // 调用真实的AI服务进行分析
      console.log('调用AI进行焦虑分析...');
      aiAnalysis = await aiAnxietyAnalysis(content);

      console.log('调用AI生成任务建议...');
      generatedTasks = await aiTaskGeneration(content, aiAnalysis);

    } catch (aiError) {
      console.error('AI分析失败，使用备用方案:', aiError.message);

      // 如果AI调用失败，回退到mock数据
      aiAnalysis = await mockAIAnalysis(content);
      generatedTasks = await mockTaskGeneration(content, aiAnalysis);

      // 在分析结果中标记使用了备用方案
      aiAnalysis.fallbackUsed = true;
      aiAnalysis.fallbackReason = aiError.message;
    }

    const processingTime = (Date.now() - startTime) / 1000;
    
    // 更新记录
    const anxietyCollection = db.collection('anxietyRecords');
    await anxietyCollection.doc(recordId).update({
      data: {
        aiAnalysis: aiAnalysis,
        generatedTasks: generatedTasks,
        status: status,
        processingTime: processingTime,
        updatedAt: new Date()
      }
    });

    // 创建任务记录
    if (generatedTasks.length > 0) {
      await createTasksFromAnalysis(generatedTasks, userId, recordId);
    }

    console.log('AI分析完成:', recordId);

    // 返回分析结果
    return {
      status: status,
      aiAnalysis: aiAnalysis,
      generatedTasks: generatedTasks,
      processingTime: processingTime
    };

  } catch (error) {
    console.error('AI分析失败:', error);

    // 更新状态为失败
    const anxietyCollection = db.collection('anxietyRecords');
    await anxietyCollection.doc(recordId).update({
      data: {
        status: 'failed',
        error: error.message,
        updatedAt: new Date()
      }
    });

    // 返回失败结果
    return {
      status: 'failed',
      error: error.message,
      aiAnalysis: null,
      generatedTasks: [],
      processingTime: 0
    };
  }
}

// 模拟AI分析
async function mockAIAnalysis(content) {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 简单的关键词分析
  const emotions = [];
  const keywords = [];
  let urgency = 'medium';
  let category = '其他';
  
  // 情绪识别
  if (content.includes('焦虑') || content.includes('担心') || content.includes('紧张')) {
    emotions.push('焦虑');
  }
  if (content.includes('压力') || content.includes('累') || content.includes('疲惫')) {
    emotions.push('压力');
  }
  if (content.includes('害怕') || content.includes('恐惧')) {
    emotions.push('恐惧');
  }
  
  // 关键词提取
  const keywordPatterns = [
    '考试', '学习', '作业', '论文', '毕业',
    '工作', '项目', '老板', '同事', '面试',
    '家庭', '父母', '孩子', '婚姻', '恋爱',
    '健康', '身体', '疾病', '医院',
    '金钱', '房子', '车子', '贷款'
  ];
  
  keywordPatterns.forEach(keyword => {
    if (content.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  // 紧急程度判断
  if (content.includes('马上') || content.includes('立即') || content.includes('紧急')) {
    urgency = 'high';
  } else if (content.includes('慢慢') || content.includes('以后') || content.includes('将来')) {
    urgency = 'low';
  }
  
  // 分类判断
  if (keywords.some(k => ['考试', '学习', '作业', '论文', '毕业'].includes(k))) {
    category = '学习';
  } else if (keywords.some(k => ['工作', '项目', '老板', '同事', '面试'].includes(k))) {
    category = '工作';
  } else if (keywords.some(k => ['家庭', '父母', '孩子', '婚姻', '恋爱'].includes(k))) {
    category = '家庭';
  } else if (keywords.some(k => ['健康', '身体', '疾病', '医院'].includes(k))) {
    category = '健康';
  }
  
  return {
    emotions: emotions.length > 0 ? emotions : ['焦虑'],
    keywords: keywords.slice(0, 5), // 最多5个关键词
    urgency: urgency,
    complexity: keywords.length > 3 ? 'high' : keywords.length > 1 ? 'medium' : 'low',
    category: category,
    confidence: 0.75 + Math.random() * 0.2 // 0.75-0.95
  };
}

// 模拟任务生成
async function mockTaskGeneration(content, analysis) {
  const tasks = [];
  const { category, urgency, keywords } = analysis;
  
  // 根据分类生成不同的任务模板
  const taskTemplates = {
    '学习': [
      { title: '制定学习计划', description: '列出需要学习的内容和时间安排', priority: 'high', estimatedTime: 30 },
      { title: '整理学习资料', description: '收集和整理相关的学习材料', priority: 'medium', estimatedTime: 20 },
      { title: '寻求帮助', description: '向老师或同学请教不懂的问题', priority: 'medium', estimatedTime: 15 }
    ],
    '工作': [
      { title: '任务分解', description: '将大任务分解为小的可执行步骤', priority: 'high', estimatedTime: 25 },
      { title: '时间规划', description: '制定详细的时间计划和里程碑', priority: 'high', estimatedTime: 20 },
      { title: '资源准备', description: '准备完成任务所需的工具和资料', priority: 'medium', estimatedTime: 15 }
    ],
    '家庭': [
      { title: '沟通交流', description: '与家人开诚布公地交流想法和感受', priority: 'high', estimatedTime: 30 },
      { title: '寻找平衡', description: '在家庭和个人需求之间找到平衡点', priority: 'medium', estimatedTime: 20 },
      { title: '制定规则', description: '建立家庭成员都认同的规则和边界', priority: 'medium', estimatedTime: 25 }
    ],
    '健康': [
      { title: '医疗咨询', description: '预约医生进行专业咨询和检查', priority: 'high', estimatedTime: 60 },
      { title: '生活调整', description: '调整作息和饮食习惯', priority: 'medium', estimatedTime: 30 },
      { title: '运动锻炼', description: '制定适合的运动计划', priority: 'low', estimatedTime: 30 }
    ]
  };
  
  // 通用任务模板
  const generalTasks = [
    { title: '深呼吸放松', description: '进行5分钟的深呼吸练习', priority: 'low', estimatedTime: 5 },
    { title: '写下担忧', description: '详细写下所有担心的事情', priority: 'medium', estimatedTime: 15 },
    { title: '寻求支持', description: '向信任的朋友或家人寻求建议', priority: 'medium', estimatedTime: 20 }
  ];
  
  // 选择合适的任务模板
  const templates = taskTemplates[category] || generalTasks;
  
  // 生成2-4个任务
  const taskCount = Math.min(templates.length, 2 + Math.floor(Math.random() * 3));
  
  for (let i = 0; i < taskCount; i++) {
    const template = templates[i];
    const task = {
      ...template,
      suggestedTime: getSuggestedTime(urgency, i)
    };
    tasks.push(task);
  }
  
  return tasks;
}

// 获取建议时间
function getSuggestedTime(urgency, index) {
  const now = new Date();
  let suggestedTime;
  
  if (urgency === 'high') {
    // 紧急任务，建议在1-2小时内开始
    suggestedTime = new Date(now.getTime() + (1 + index) * 60 * 60 * 1000);
  } else if (urgency === 'medium') {
    // 中等紧急，建议在今天或明天
    suggestedTime = new Date(now.getTime() + (index + 1) * 4 * 60 * 60 * 1000);
  } else {
    // 不紧急，建议在未来几天
    suggestedTime = new Date(now.getTime() + (index + 1) * 24 * 60 * 60 * 1000);
  }
  
  return suggestedTime.toISOString();
}

// 从分析结果创建任务
async function createTasksFromAnalysis(generatedTasks, userId, anxietyRecordId) {
  try {
    const tasksCollection = db.collection('tasks');
    const tasks = generatedTasks.map(task => ({
      userId: userId,
      anxietyRecordId: anxietyRecordId,
      title: task.title,
      description: task.description,
      priority: task.priority,
      category: '焦虑管理',
      estimatedTime: task.estimatedTime,
      dueDate: new Date(task.suggestedTime),
      status: 'pending',
      progress: 0,
      tags: ['AI生成'],
      reminder: {
        enabled: true,
        time: new Date(task.suggestedTime),
        sent: false
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }));
    
    // 批量创建任务
    for (const task of tasks) {
      await tasksCollection.add({ data: task });
    }
    
    console.log(`创建了 ${tasks.length} 个任务`);
    
  } catch (error) {
    console.error('创建任务失败:', error);
  }
}

// 更新用户统计
async function updateUserStats(userId, type) {
  try {
    const usersCollection = db.collection('users');
    const updateData = {
      updatedAt: new Date()
    };
    
    if (type === 'anxietyRecord') {
      updateData['statistics.totalAnxietyRecords'] = db.command.inc(1);
    }
    
    await usersCollection.where({
      openid: userId
    }).update({
      data: updateData
    });
    
  } catch (error) {
    console.error('更新用户统计失败:', error);
  }
}

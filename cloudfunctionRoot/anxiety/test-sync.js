// 测试同步处理的焦虑分析功能
const axios = require('axios');

// 模拟云开发环境
const mockCloud = {
  database: () => ({
    collection: (name) => ({
      add: async (data) => ({ _id: 'test_record_id' }),
      doc: (id) => ({
        update: async (data) => ({ success: true }),
        get: async () => ({ data: { userId: 'test_user', status: 'completed' } })
      }),
      where: () => ({
        update: async (data) => ({ success: true })
      })
    })
  }),
  getWXContext: () => ({ OPENID: 'test_user_openid' })
};

// 硅基智能API配置
const SILICONFLOW_CONFIG = {
  baseURL: 'https://api.siliconflow.cn/v1',
  model: 'deepseek-ai/DeepSeek-V3',
  apiKey: process.env.AI_API_KEY || 'sk-enejitcrukxwnxpsxoeyhyssenfdapwcwybjwqhwqryilvqc',
  timeout: 30000,
  maxRetries: 3
};

// AI分析提示词模板
const AI_PROMPTS = {
  anxietyAnalysis: `你是一位专业的心理健康分析师。请分析用户的焦虑内容，并返回结构化的分析结果。

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "emotions": ["情绪1", "情绑2"],
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "urgency": "low|medium|high",
  "complexity": "low|medium|high", 
  "category": "学习|工作|家庭|健康|人际关系|其他",
  "confidence": 0.85
}

分析要求：
1. emotions: 识别用户的主要情绪状态（如焦虑、压力、恐惧、担心等）
2. keywords: 提取3-5个关键词，反映焦虑的核心内容
3. urgency: 评估紧急程度（low=不紧急，medium=中等紧急，high=非常紧急）
4. complexity: 评估问题复杂程度（low=简单，medium=中等，high=复杂）
5. category: 将焦虑内容分类到最相关的类别
6. confidence: 分析结果的置信度（0-1之间的小数）

用户焦虑内容：`,

  taskGeneration: `你是一位专业的心理健康顾问和任务管理专家。基于用户的焦虑分析结果，请生成2-4个具体可执行的任务建议。

请严格按照以下JSON格式返回任务列表，不要包含任何其他文字：

{
  "tasks": [
    {
      "title": "任务标题",
      "description": "详细的任务描述，说明具体要做什么",
      "priority": "low|medium|high",
      "estimatedTime": 30,
      "category": "immediate|short_term|long_term"
    }
  ]
}

任务生成要求：
1. 任务要具体、可执行、有针对性
2. 根据焦虑的紧急程度和复杂度调整任务数量和类型
3. 包含立即可做的缓解措施和长期解决方案
4. estimatedTime为预估完成时间（分钟）
5. category分为immediate（立即执行）、short_term（短期内）、long_term（长期规划）

焦虑分析结果：
`
};

// 调用硅基智能API
async function callSiliconFlowAPI(messages, retryCount = 0) {
  try {
    if (!SILICONFLOW_CONFIG.apiKey) {
      throw new Error('AI_API_KEY环境变量未配置');
    }

    console.log('发送API请求，模型:', 'deepseek-ai/DeepSeek-V3');
    console.log('API Key前缀:', SILICONFLOW_CONFIG.apiKey.substring(0, 10) + '...');
    
    const response = await axios.post(
      `https://api.siliconflow.cn/v1/chat/completions`,
      {
        model: "deepseek-ai/DeepSeek-V3",
        messages: messages,
        temperature: 0.1,
        max_tokens: 4000,
        stream: false
      },
      {
        headers: {
          'Authorization': `Bearer ${SILICONFLOW_CONFIG.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: SILICONFLOW_CONFIG.timeout
      }
    );

    if (response.data && response.data.choices && response.data.choices[0]) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('调用硅基智能API失败:', error.message);
    
    // 详细错误信息
    if (error.response) {
      console.error('API响应状态:', error.response.status);
      console.error('API响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    
    // 重试机制
    if (retryCount < SILICONFLOW_CONFIG.maxRetries) {
      console.log(`第${retryCount + 1}次重试...`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return callSiliconFlowAPI(messages, retryCount + 1);
    }
    
    throw error;
  }
}

// 使用AI进行焦虑分析
async function aiAnxietyAnalysis(content) {
  try {
    const messages = [
      {
        role: 'user',
        content: AI_PROMPTS.anxietyAnalysis + content
      }
    ];

    const response = await callSiliconFlowAPI(messages);
    
    try {
      // 清理响应内容，移除可能的markdown格式
      let cleanResponse = response.trim();

      // 移除markdown代码块标记
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const analysisResult = JSON.parse(cleanResponse);

      if (!analysisResult.emotions || !analysisResult.keywords || !analysisResult.urgency) {
        throw new Error('AI返回结果格式不完整');
      }

      return analysisResult;
    } catch (parseError) {
      console.error('解析AI分析结果失败:', parseError.message);
      console.error('原始响应:', response);
      throw new Error('AI返回结果格式错误');
    }
  } catch (error) {
    console.error('AI焦虑分析失败:', error.message);
    throw error;
  }
}

// 测试同步处理
async function testSyncProcessing() {
  console.log('=== 测试同步处理 ===');
  
  const testContent = "我明天要面试了，很紧张，担心表现不好，一直在想可能出现的问题，睡不着觉。";
  
  try {
    console.log('测试内容:', testContent);
    console.log('开始同步分析...');
    
    const startTime = Date.now();
    const analysis = await aiAnxietyAnalysis(testContent);
    const endTime = Date.now();
    
    console.log('分析完成，耗时:', (endTime - startTime) / 1000, '秒');
    console.log('分析结果:', JSON.stringify(analysis, null, 2));
    
    return analysis;
  } catch (error) {
    console.error('同步处理测试失败:', error.message);
    return null;
  }
}

// 运行测试
if (require.main === module) {
  testSyncProcessing();
}

module.exports = {
  testSyncProcessing
};

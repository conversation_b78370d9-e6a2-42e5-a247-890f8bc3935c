<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>焦虑倾诉页面优化预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .anxiety-dump-page {
            min-height: 100vh;
        }
        
        /* 页面头部 */
        .anxiety-dump-header {
            padding: 30px 16px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        
        .anxiety-dump-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }
        
        .header-content {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            position: relative;
            z-index: 2;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .care-icon {
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
        }
        
        .header-text {
            flex: 1;
        }
        
        .page-title {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }
        
        .page-subtitle {
            font-size: 13px;
            color: rgba(255,255,255,0.8);
            line-height: 1.4;
        }
        
        .tip-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 12px;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .tip-icon {
            font-size: 16px;
            margin-right: 8px;
        }
        
        .tip-content {
            flex: 1;
        }
        
        .tip-title {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 2px;
        }
        
        .tip-text {
            font-size: 11px;
            color: rgba(255,255,255,0.7);
            line-height: 1.4;
        }
        
        /* 主要内容区域 */
        .main-content {
            flex: 1;
            padding: 16px;
        }
        
        .input-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            margin-bottom: 12px;
        }
        
        .input-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .input-label {
            font-size: 14px;
            font-weight: 600;
            color: #1a202c;
        }
        
        .word-count-badge {
            background: #f7fafc;
            border-radius: 10px;
            padding: 4px 8px;
            display: flex;
            align-items: center;
        }
        
        .word-count {
            font-size: 12px;
            font-weight: 600;
            color: #4a5568;
            margin-right: 2px;
        }
        
        .word-unit {
            font-size: 10px;
            color: #718096;
        }
        
        .text-input {
            width: 100%;
            min-height: 160px;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
            background: #f8fafc;
            color: #2d3748;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .text-input:focus {
            border-color: #667eea;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .action-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .btn {
            flex: 1;
            height: 44px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-clear {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-clear:hover {
            background: #edf2f7;
        }
        
        .btn-submit {
            background: #e2e8f0;
            color: #a0aec0;
            border: 1px solid #e2e8f0;
        }
        
        .btn-submit.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .btn-submit.active:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .analysis-info {
            text-align: center;
            padding: 8px;
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border-radius: 6px;
            border: 1px solid #9ae6b4;
        }
        
        .info-text {
            font-size: 12px;
            color: #2f855a;
            line-height: 1.4;
        }
        
        /* 分析进度 */
        .analysis-progress {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #bae6fd;
            animation: pulse-bg 2s infinite;
        }
        
        @keyframes pulse-bg {
            0%, 100% { background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); }
            50% { background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%); }
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .progress-title {
            font-size: 13px;
            font-weight: 500;
            color: #0369a1;
        }
        
        .progress-percent {
            font-size: 12px;
            font-weight: 600;
            color: #0284c7;
        }
        
        .progress-bar {
            height: 4px;
            background: #e0f2fe;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 6px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
            border-radius: 2px;
            transition: width 0.5s ease;
            width: 65%;
        }
        
        .progress-step {
            font-size: 11px;
            color: #0369a1;
            text-align: center;
            display: block;
        }
    </style>
</head>
<body>
    <div class="anxiety-dump-page">
        <!-- 页面头部 -->
        <div class="anxiety-dump-header">
            <div class="header-content">
                <div class="header-icon">
                    <div class="care-icon"></div>
                </div>
                <div class="header-text">
                    <span class="page-title">倾诉你的焦虑</span>
                    <span class="page-subtitle">AI会倾听并帮你找到解决方案</span>
                </div>
            </div>
            
            <!-- 温馨提示卡片 -->
            <div class="tip-card">
                <div class="tip-icon">💡</div>
                <div class="tip-content">
                    <span class="tip-title">温馨提示</span>
                    <span class="tip-text">你的倾诉内容完全私密，只有你能看到</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="input-header">
                    <span class="input-label">说出你的困扰</span>
                    <div class="word-count-badge">
                        <span class="word-count">156</span>
                        <span class="word-unit">字</span>
                    </div>
                </div>
                
                <div class="input-container">
                    <textarea 
                        class="text-input"
                        placeholder="在这里，你可以安全地表达内心的焦虑和困扰..."
                    >我最近工作压力很大，总是担心完不成项目，每天都很焦虑，不知道该怎么办...</textarea>
                </div>
            </div>

            <!-- 操作区域 -->
            <div class="action-section">
                <div class="action-buttons">
                    <button class="btn btn-clear">清空</button>
                    <button class="btn btn-submit active">开始AI分析</button>
                </div>
                
                <!-- 分析说明 -->
                <div class="analysis-info" id="analysis-info">
                    <span class="info-text">🤖 AI将为你分析情绪并生成专属解决方案</span>
                </div>
                
                <!-- 分析进度 -->
                <div class="analysis-progress" id="analysis-progress" style="display: none;">
                    <div class="progress-header">
                        <span class="progress-title">AI正在分析中...</span>
                        <span class="progress-percent">65%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <span class="progress-step">识别关键问题...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟分析按钮点击
        document.querySelector('.btn-submit').addEventListener('click', function() {
            if (this.classList.contains('active')) {
                document.getElementById('analysis-info').style.display = 'none';
                document.getElementById('analysis-progress').style.display = 'block';
                this.textContent = 'AI分析中...';
                this.classList.remove('active');
            }
        });
    </script>
</body>
</html>

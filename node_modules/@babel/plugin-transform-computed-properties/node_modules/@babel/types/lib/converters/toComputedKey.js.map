{"version": 3, "names": ["_index", "require", "_index2", "toCom<PERSON><PERSON>ey", "node", "key", "property", "computed", "isIdentifier", "stringLiteral", "name"], "sources": ["../../src/converters/toComputedKey.ts"], "sourcesContent": ["import { isIdentifier } from \"../validators/generated/index.ts\";\nimport { stringLiteral } from \"../builders/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function toComputedKey(\n  node:\n    | t.ObjectMember\n    | t.ObjectProperty\n    | t.ClassMethod\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.MemberExpression\n    | t.OptionalMemberExpression,\n  // @ts-expect-error todo(flow->ts): maybe check the type of node before accessing .key and .property\n  key: t.Expression | t.PrivateName = node.key || node.property,\n) {\n  if (!node.computed && isIdentifier(key)) key = stringLiteral(key.name);\n\n  return key;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAGe,SAASE,aAAaA,CACnCC,IAO8B,EAE9BC,GAAiC,GAAGD,IAAI,CAACC,GAAG,IAAID,IAAI,CAACE,QAAQ,EAC7D;EACA,IAAI,CAACF,IAAI,CAACG,QAAQ,IAAI,IAAAC,mBAAY,EAACH,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAAI,qBAAa,EAACJ,GAAG,CAACK,IAAI,CAAC;EAEtE,OAAOL,GAAG;AACZ", "ignoreList": []}
'use strict';
require('../../modules/es.symbol');
require('../../modules/es.object.assign');
require('../../modules/es.object.create');
require('../../modules/es.object.define-property');
require('../../modules/es.object.define-properties');
require('../../modules/es.object.entries');
require('../../modules/es.object.freeze');
require('../../modules/es.object.from-entries');
require('../../modules/es.object.get-own-property-descriptor');
require('../../modules/es.object.get-own-property-descriptors');
require('../../modules/es.object.get-own-property-names');
require('../../modules/es.object.get-prototype-of');
require('../../modules/es.object.group-by');
require('../../modules/es.object.has-own');
require('../../modules/es.object.is');
require('../../modules/es.object.is-extensible');
require('../../modules/es.object.is-frozen');
require('../../modules/es.object.is-sealed');
require('../../modules/es.object.keys');
require('../../modules/es.object.prevent-extensions');
require('../../modules/es.object.proto');
require('../../modules/es.object.seal');
require('../../modules/es.object.set-prototype-of');
require('../../modules/es.object.values');
require('../../modules/es.object.to-string');
require('../../modules/es.object.define-getter');
require('../../modules/es.object.define-setter');
require('../../modules/es.object.lookup-getter');
require('../../modules/es.object.lookup-setter');
require('../../modules/es.json.to-string-tag');
require('../../modules/es.math.to-string-tag');
require('../../modules/es.reflect.to-string-tag');
var path = require('../../internals/path');

module.exports = path.Object;
